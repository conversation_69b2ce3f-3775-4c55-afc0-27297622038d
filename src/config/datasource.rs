use anyhow::Context;
use oracle::Connection;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct Datasource {
    url: String,
    username: String,
    password: String,
}

impl Datasource {
    pub fn new(url: String, username: String, password: String) -> Self {
        Self {
            url,
            username,
            password,
        }
    }
    
    pub fn init_form_config() -> anyhow::Result<Self> {
        config::Config::builder()
            .add_source(config::File::with_name("application.toml"))
            .add_source(config::Environment::with_prefix("DS"))
            .build()
            .and_then(|config| config.try_deserialize())
            .with_context(|| "failed to load datasource configuration file")
    }
    
    pub fn connect(&self) -> anyhow::Result<Connection> {
        Connection::connect(&self.username, &self.password, &self.url)
            .with_context(|| "failed to connect to datasource")
    }
}